package com.boryou.web.service;

import cn.hutool.core.text.CharSequenceUtil;
import com.boryou.common.exception.CustomException;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.extractor.WordExtractor;
import org.apache.poi.hwpf.usermodel.Paragraph;
import org.apache.poi.hwpf.usermodel.Range;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.List;

@Service
@Slf4j
public class FileOperateService {

    public String importDoc(MultipartFile file) {
        String text = "";
        String name = "";
        try {
            // 获取文件后缀名
            String fileName = file.getOriginalFilename();
            if (CharSequenceUtil.isBlank(fileName)) {
                throw new CustomException("文件名不能为空");
            }
            if (fileName.lastIndexOf(".") != -1 && fileName.lastIndexOf(".") != 0) {
                name = fileName.substring(fileName.lastIndexOf(".") + 1);
            }
            InputStream inputStream = file.getInputStream();

            // 非utf-8编码，进行转码
            ZipSecureFile.setMinInflateRatio(-1.0d);
            if (name.contains("docx")) {
                text = this.getContentDocx(inputStream);
                if (CharSequenceUtil.isBlank(text)) {
                    text = this.getContentDoc(inputStream);
                }
            } else if (name.contains("doc")) {
                text = this.getContentDoc(inputStream);
                if (CharSequenceUtil.isBlank(text)) {
                    text = this.getContentDocx(inputStream);
                }
            }
            inputStream.close();
            log.warn("文件名为: {}, 转换文本为: {}", fileName, text);
        } catch (Exception e) {
            log.error("上传失败: {}", e.getMessage(), e);
            throw new CustomException("上传失败");
        }
        return text;
    }

    /**
     * 获取正文文件内容，docx方法
     *
     * @param file
     * @return
     */
    public String getContentDocx(InputStream is) {
        StringBuilder content = new StringBuilder();
        try (XWPFDocument xwpf = new XWPFDocument(is)) {
            // 2007版本的word
            // 2007版本，仅支持docx文件处理
            List<XWPFParagraph> paragraphs = xwpf.getParagraphs();
            if (!paragraphs.isEmpty()) {
                for (XWPFParagraph paragraph : paragraphs) {
                    if (!paragraph.getParagraphText().startsWith("    ")) {
                        content.append(paragraph.getParagraphText().trim()).append("\n\t");
                    } else {
                        content.append(paragraph.getParagraphText());
                    }
                }
            }
        } catch (Exception e) {
            log.error("docx解析正文异常:{}", e.getMessage(), e);
        }
        return content.toString();
    }

    /**
     * 获取正文文件内容，doc方法
     *
     * @param is 输入流，不能为null
     * @return 解析后的文档内容，保持原有格式
     * @throws CustomException 当输入流为null或解析过程中发生严重错误时抛出
     */
    public String getContentDoc(InputStream is) {
        if (is == null) {
            throw new CustomException("输入流不能为空");
        }

        StringBuilder content = new StringBuilder();
        try {
            // 使用HWPFDocument获取更详细的格式信息
            HWPFDocument document = new HWPFDocument(is);
            Range range = document.getRange();

            // 遍历所有段落，保持原有格式
            int numParagraphs = range.numParagraphs();
            for (int i = 0; i < numParagraphs; i++) {
                Paragraph paragraph = range.getParagraph(i);
                processParagraphWithFormat(content, paragraph);
            }

            document.close();

        } catch (Exception e) {
            log.error("doc文档解析失败: {}", e.getMessage(), e);
            // 降级到简单的WordExtractor方式
            return fallbackToWordExtractor(is);
        }

        return content.toString().trim();
    }

    /**
     * 降级处理方法，使用WordExtractor
     */
    private String fallbackToWordExtractor(InputStream is) {
        StringBuilder content = new StringBuilder();
        try (WordExtractor extractor = new WordExtractor(is)) {
            String[] paragraphTexts = extractor.getParagraphText();
            if (paragraphTexts != null && paragraphTexts.length > 0) {
                processSimpleParagraphs(content, paragraphTexts);
            }
        } catch (Exception e) {
            log.error("WordExtractor降级处理也失败: {}", e.getMessage(), e);
            throw new CustomException("doc文档解析失败: " + e.getMessage());
        }
        return content.toString().trim();
    }

    /**
     * 处理带格式信息的段落
     *
     * @param content 内容构建器
     * @param paragraph POI段落对象
     */
    private void processParagraphWithFormat(StringBuilder content, Paragraph paragraph) {
        String paragraphText = paragraph.text();

        if (CharSequenceUtil.isBlank(paragraphText)) {
            return; // 跳过空段落
        }

        // 获取段落的缩进信息
        String indentString = calculateIndentFromParagraph(paragraph);

        // 清理段落文本（移除特殊字符）
        String cleanText = cleanParagraphText(paragraphText);

        if (CharSequenceUtil.isNotBlank(cleanText)) {
            content.append(indentString)
                   .append(cleanText)
                   .append(System.lineSeparator());
        }
    }

    /**
     * 根据段落属性计算缩进字符串
     *
     * @param paragraph POI段落对象
     * @return 缩进字符串
     */
    private String calculateIndentFromParagraph(Paragraph paragraph) {
        try {
            // 获取段落属性
            int firstLineIndent = paragraph.getFirstLineIndent();
            int leftIndent = paragraph.getIndentFromLeft();

            // 将缇（twips）转换为字符数（大约20缇 = 1个字符宽度）
            int indentChars = Math.max(0, (leftIndent + firstLineIndent) / 240); // 240缇约等于一个中文字符

            // 生成缩进字符串
            if (indentChars > 0) {
                return generateIndentString(Math.min(indentChars, 10)); // 最多10级缩进
            }

            // 如果无法获取精确缩进，根据文本特征判断
            String text = paragraph.text();
            if (text.startsWith("\t") || text.startsWith("    ")) {
                return "    "; // 标准缩进
            }

        } catch (Exception e) {
            log.debug("获取段落缩进信息失败，使用默认处理: {}", e.getMessage());
        }

        return ""; // 无缩进
    }

    /**
     * 生成缩进字符串（Java 8兼容版本）
     *
     * @param count 缩进级别
     * @return 缩进字符串
     */
    private String generateIndentString(int count) {
        if (count <= 0) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append("    "); // 4个空格为一级缩进
        }
        return sb.toString();
    }

    /**
     * 清理段落文本，移除特殊控制字符
     *
     * @param text 原始文本
     * @return 清理后的文本
     */
    private String cleanParagraphText(String text) {
        if (text == null) {
            return "";
        }

        // 移除Word特殊字符（如段落标记、制表符等）
        return text.replaceAll("[\r\n\u0007\u0013\u0014\u0015]", "")
                  .trim();
    }

    /**
     * 简单段落处理（降级方案）
     *
     * @param content 内容构建器
     * @param paragraphTexts 段落文本数组
     */
    private void processSimpleParagraphs(StringBuilder content, String[] paragraphTexts) {
        for (String paragraph : paragraphTexts) {
            if (CharSequenceUtil.isBlank(paragraph)) {
                continue;
            }

            String cleanText = cleanParagraphText(paragraph);
            if (CharSequenceUtil.isNotBlank(cleanText)) {
                // 简单的缩进判断
                String indent = paragraph.startsWith("    ") || paragraph.startsWith("\t") ? "    " : "";
                content.append(indent)
                       .append(cleanText)
                       .append(System.lineSeparator());
            }
        }
    }

}
