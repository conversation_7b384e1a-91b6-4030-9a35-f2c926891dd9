package com.boryou.web.module.tag.entity.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 标签VO类
 * 
 * <AUTHOR>
 */
@Data
public class TagVO {

    /**
     * 主键id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @TableId(type = IdType.NONE)
    private Long id;

    /**
     * 用户ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 标签内容
     */
    @NotBlank(message = "标签内容不能为空")
    private String tagContent;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;
}
