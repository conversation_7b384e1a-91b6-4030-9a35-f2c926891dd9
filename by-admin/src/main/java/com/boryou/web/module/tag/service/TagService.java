package com.boryou.web.module.tag.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.exception.CustomException;
import com.boryou.web.module.search.entity.ChangeData;
import com.boryou.web.module.search.enums.ChangeEnum;
import com.boryou.web.module.search.mapper.ChangeDataMapper;
import com.boryou.web.module.tag.entity.Tag;
import com.boryou.web.module.tag.entity.vo.TagQueryVO;
import com.boryou.web.module.tag.entity.vo.TagVO;
import com.boryou.web.module.tag.mapper.TagMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class TagService extends ServiceImpl<TagMapper, Tag> {

    private final ChangeDataMapper changeDataMapper;

    /**
     * 新增或更新标签
     *
     * @param tagVO 标签VO
     * @param user  当前用户
     */
    public void saveOrUpdateTag(TagVO tagVO, SysUser user) {
        String tagContent = tagVO.getTagContent();
        Long tagId = tagVO.getId();
        Long userId = user.getUserId();
        String userName = user.getUserName();

        if (CharSequenceUtil.isBlank(tagContent)) {
            throw new CustomException("标签内容不能为空");
        }

        DateTime date = DateUtil.date();

        // 检查新内容是否与用户其他标签重复（排除当前标签）
        Integer duplicateTagCount = this.lambdaQuery()
                .eq(Tag::getTagContent, tagContent)
                .eq(Tag::getUserId, userId)
                .eq(Tag::getDelFlag, "0")
                .ne(tagId != null, Tag::getId, tagId)
                .count();

        if (duplicateTagCount != null && duplicateTagCount > 0) {
            throw new CustomException("标签内容已存在");
        }

        if (tagId != null) {
            // 更新模式：通过ID查询
            Tag existingTag = this.getById(tagId);

            if (existingTag == null) {
                throw new CustomException("标签不存在");
            }
            Long userIdExisting = existingTag.getUserId();
            // 检查权限
            if (!Objects.equals(userIdExisting, userId)) {
                throw new CustomException("无权限操作该标签");
            }

            boolean updateFlag = false;
            String delFlagExisting = existingTag.getDelFlag();
            String tagContentExisting = existingTag.getTagContent();

            if (!Objects.equals(delFlagExisting, "0")) {
                existingTag.setDelFlag("0");
                updateFlag = true;
            }
            // 检查标签内容是否有变化
            if (!Objects.equals(tagContentExisting, tagContent)) {
                existingTag.setTagContent(tagContent);
                updateFlag = true;
            }
            if (!updateFlag) {
                return;
            }
            // 更新标签
            existingTag.setUpdateBy(userName);
            existingTag.setUpdateTime(date);

            this.updateById(existingTag);

        } else {
            // 新增标签
            Tag tag = new Tag();
            tag.setId(IdUtil.getSnowflakeNextId());
            tag.setUserId(userId);
            tag.setTagContent(tagContent);
            tag.setCreateBy(userName);
            tag.setCreateTime(date);
            tag.setUpdateBy(userName);
            tag.setUpdateTime(date);
            tag.setDelFlag("0");
            this.save(tag);
        }

    }

    /**
     * 查询标签列表（统一查询接口）
     *
     * @param queryVO       查询条件
     * @param currentUserId 当前用户ID
     * @return 标签列表或分页结果
     */
    public Object queryTags(TagQueryVO queryVO, Long currentUserId) {
        if (queryVO == null) {
            queryVO = new TagQueryVO();
        }

        LambdaQueryWrapper<Tag> queryWrapper = new LambdaQueryWrapper<>();

        // 删除标志过滤
        queryWrapper.eq(Tag::getDelFlag, StrUtil.isNotBlank(queryVO.getDelFlag()) ? queryVO.getDelFlag() : "0");

        // 用户ID过滤（如果不传userId则查询当前用户的标签）
        Long targetUserId = queryVO.getUserId() != null ? queryVO.getUserId() : currentUserId;
        if (targetUserId != null) {
            queryWrapper.eq(Tag::getUserId, targetUserId);
        }

        // 标签内容模糊查询
        if (StrUtil.isNotBlank(queryVO.getTagContent())) {
            queryWrapper.like(Tag::getTagContent, queryVO.getTagContent());
        }

        // 创建者查询
        if (StrUtil.isNotBlank(queryVO.getCreateBy())) {
            queryWrapper.like(Tag::getCreateBy, queryVO.getCreateBy());
        }

        // 创建时间范围查询
        if (queryVO.getCreateTimeStart() != null) {
            queryWrapper.ge(Tag::getCreateTime, queryVO.getCreateTimeStart());
        }
        if (queryVO.getCreateTimeEnd() != null) {
            queryWrapper.le(Tag::getCreateTime, queryVO.getCreateTimeEnd());
        }

        // 更新时间范围查询
        if (queryVO.getUpdateTimeStart() != null) {
            queryWrapper.ge(Tag::getUpdateTime, queryVO.getUpdateTimeStart());
        }
        if (queryVO.getUpdateTimeEnd() != null) {
            queryWrapper.le(Tag::getUpdateTime, queryVO.getUpdateTimeEnd());
        }

        // 排序处理
        String orderBy = StrUtil.isNotBlank(queryVO.getOrderBy()) ? queryVO.getOrderBy() : "updateTime";
        String orderDirection = StrUtil.isNotBlank(queryVO.getOrderDirection()) ? queryVO.getOrderDirection() : "desc";

        switch (orderBy) {
            case "createTime":
                if ("asc".equalsIgnoreCase(orderDirection)) {
                    queryWrapper.orderByAsc(Tag::getCreateTime);
                } else {
                    queryWrapper.orderByDesc(Tag::getCreateTime);
                }
                break;
            case "tagContent":
                if ("asc".equalsIgnoreCase(orderDirection)) {
                    queryWrapper.orderByAsc(Tag::getTagContent);
                } else {
                    queryWrapper.orderByDesc(Tag::getTagContent);
                }
                break;
            case "updateTime":
            default:
                if ("asc".equalsIgnoreCase(orderDirection)) {
                    queryWrapper.orderByAsc(Tag::getUpdateTime);
                } else {
                    queryWrapper.orderByDesc(Tag::getUpdateTime);
                }
                break;
        }

        // 判断是否需要分页
        if (queryVO.getPageNum() != null && queryVO.getPageSize() != null) {
            // 分页查询
            Page<Tag> page = new Page<>(queryVO.getPageNum(), queryVO.getPageSize());
            return this.page(page, queryWrapper);
        } else {
            // 不分页，返回列表
            return this.list(queryWrapper);
        }
    }

    /**
     * 删除标签（支持单个和批量）
     *
     * @param ids  标签ID列表
     * @param user 当前用户
     */
    public void deleteTags(List<Long> ids, SysUser user) {
        if (CollUtil.isEmpty(ids)) {
            throw new CustomException("标签ID列表不能为空");
        }

        // 查询要删除的标签
        List<Tag> tags = this.listByIds(ids);
        if (CollUtil.isEmpty(tags)) {
            throw new CustomException("标签不存在");
        }

        // 检查找到的标签数量是否与请求的ID数量一致
        if (tags.size() != ids.size()) {
            List<Long> foundIds = tags.stream().map(Tag::getId).collect(Collectors.toList());
            List<Long> notFoundIds = ids.stream()
                    .filter(id -> !foundIds.contains(id))
                    .collect(Collectors.toList());
            throw new CustomException("部分标签不存在，ID: " + notFoundIds);
        }

        DateTime date = DateUtil.date();
        String userName = user.getUserName();
        Long userId = user.getUserId();

        // 查询标签是否关联数据 - 使用直接查询避免循环依赖
        List<String> stringIds = ids.stream().map(String::valueOf).collect(Collectors.toList());
        long count = changeDataMapper.selectCount(
                new LambdaQueryWrapper<ChangeData>()
                        .eq(ChangeData::getChangeType, ChangeEnum.TAG.getType())
                        .in(ChangeData::getChangeValue, stringIds)
                        .eq(ChangeData::getUserId, userId)
        );
        if (count > 0) {
            throw new CustomException("标签已被使用，请解绑后再进行删除");
        }

        // 检查权限并更新删除标志
        tags.forEach(tag -> {
            // 检查是否为当前用户的标签
            if (!tag.getUserId().equals(userId)) {
                throw new CustomException("无权限删除标签：" + tag.getTagContent());
            }

            // 检查是否已经删除
            if ("2".equals(tag.getDelFlag())) {
                throw new CustomException("标签已被删除：" + tag.getTagContent());
            }

            tag.setDelFlag("2");
            tag.setUpdateBy(userName);
            tag.setUpdateTime(date);
        });

        // 批量更新
        this.updateBatchById(tags);
    }

    /**
     * 根据ID列表获取标签详情（支持单个和批量）
     *
     * @param ids    标签ID列表
     * @param userId 用户ID
     * @return 标签列表或单个标签
     */
    public Object getTagsByIds(List<Long> ids, Long userId) {
        if (CollUtil.isEmpty(ids)) {
            throw new CustomException("标签ID列表不能为空");
        }

        // 查询标签
        List<Tag> tags = this.lambdaQuery()
                .in(Tag::getId, ids)
                .eq(Tag::getDelFlag, "0")
                .list();

        if (CollUtil.isEmpty(tags)) {
            throw new CustomException("标签不存在");
        }

        // 检查权限
        if (userId != null) {
            tags.forEach(tag -> {
                if (!tag.getUserId().equals(userId)) {
                    throw new CustomException("无权限查看标签：" + tag.getTagContent());
                }
            });
        }

        // 检查是否所有ID都找到了
        if (tags.size() != ids.size()) {
            List<Long> foundIds = tags.stream().map(Tag::getId).collect(Collectors.toList());
            List<Long> notFoundIds = ids.stream()
                    .filter(id -> !foundIds.contains(id))
                    .collect(Collectors.toList());
            throw new CustomException("部分标签不存在，ID: " + notFoundIds);
        }

        // 如果只查询一个ID，返回单个对象；否则返回列表
        if (ids.size() == 1) {
            return tags.get(0);
        } else {
            return tags;
        }
    }
}
