package com.boryou.web.module.tag.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.exception.CustomException;
import com.boryou.web.module.tag.entity.Tag;
import com.boryou.web.module.tag.entity.vo.TagVO;
import com.boryou.web.module.tag.mapper.TagMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class TagService extends ServiceImpl<TagMapper, Tag> {

    /**
     * 新增或更新标签
     *
     * @param tagVO 标签VO
     * @param user 当前用户
     */
    public void saveOrUpdateTag(TagVO tagVO, SysUser user) {
        String tagContent = tagVO.getTagContent();
        Long userId = user.getUserId();
        String userName = user.getUserName();

        if (StrUtil.isBlank(tagContent)) {
            throw new CustomException("标签内容不能为空");
        }

        DateTime date = DateUtil.date();

        // 查询是否已存在相同标签内容的记录
        Tag existingTag = this.lambdaQuery()
                .eq(Tag::getTagContent, tagContent)
                .eq(Tag::getUserId, userId)
                .eq(Tag::getDelFlag, 0)
                .one();

        if (existingTag == null) {
            // 新增
            Tag tag = new Tag();
            tag.setId(IdUtil.getSnowflakeNextId());
            tag.setUserId(userId);
            tag.setTagContent(tagContent);
            tag.setCreateBy(userName);
            tag.setCreateTime(date);
            tag.setUpdateBy(userName);
            tag.setUpdateTime(date);
            tag.setDelFlag("0");
            this.save(tag);
        } else {
            // 更新
            existingTag.setUpdateBy(userName);
            existingTag.setUpdateTime(date);
            this.updateById(existingTag);
        }
    }

    /**
     * 根据标签内容查询标签列表
     *
     * @param tagContent 标签内容（支持模糊查询）
     * @param userId 用户ID
     * @return 标签列表
     */
    public List<Tag> queryTagsByContent(String tagContent, Long userId) {
        LambdaQueryWrapper<Tag> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Tag::getDelFlag, "0");

        if (userId != null) {
            queryWrapper.eq(Tag::getUserId, userId);
        }

        if (StrUtil.isNotBlank(tagContent)) {
            queryWrapper.like(Tag::getTagContent, tagContent);
        }

        queryWrapper.orderByDesc(Tag::getUpdateTime);

        return this.list(queryWrapper);
    }

    /**
     * 根据用户ID查询所有标签
     *
     * @param userId 用户ID
     * @return 标签列表
     */
    public List<Tag> queryTagsByUserId(Long userId) {
        if (userId == null) {
            throw new CustomException("用户ID不能为空");
        }

        return this.lambdaQuery()
                .eq(Tag::getUserId, userId)
                .eq(Tag::getDelFlag, "0")
                .orderByDesc(Tag::getUpdateTime)
                .list();
    }

    /**
     * 逻辑删除标签
     *
     * @param id 标签ID
     * @param user 当前用户
     */
    public void deleteTag(Long id, SysUser user) {
        if (id == null) {
            throw new CustomException("标签ID不能为空");
        }

        Tag tag = this.getById(id);
        if (tag == null) {
            throw new CustomException("标签不存在");
        }

        // 检查是否为当前用户的标签
        if (!tag.getUserId().equals(user.getUserId())) {
            throw new CustomException("无权限删除该标签");
        }

        DateTime date = DateUtil.date();
        tag.setDelFlag("2");
        tag.setUpdateBy(user.getUserName());
        tag.setUpdateTime(date);

        this.updateById(tag);
    }

    /**
     * 批量逻辑删除标签
     *
     * @param ids 标签ID列表
     * @param user 当前用户
     */
    public void batchDeleteTags(List<Long> ids, SysUser user) {
        if (CollUtil.isEmpty(ids)) {
            throw new CustomException("标签ID列表不能为空");
        }

        List<Tag> tags = this.listByIds(ids);
        if (CollUtil.isEmpty(tags)) {
            throw new CustomException("标签不存在");
        }

        DateTime date = DateUtil.date();
        String userName = user.getUserName();
        Long userId = user.getUserId();

        for (Tag tag : tags) {
            // 检查是否为当前用户的标签
            if (!tag.getUserId().equals(userId)) {
                throw new CustomException("无权限删除标签：" + tag.getTagContent());
            }

            tag.setDelFlag("2");
            tag.setUpdateBy(userName);
            tag.setUpdateTime(date);
        }

        this.updateBatchById(tags);
    }

    /**
     * 根据ID获取标签详情
     *
     * @param id 标签ID
     * @param userId 用户ID
     * @return 标签详情
     */
    public Tag getTagById(Long id, Long userId) {
        if (id == null) {
            throw new CustomException("标签ID不能为空");
        }

        Tag tag = this.lambdaQuery()
                .eq(Tag::getId, id)
                .eq(Tag::getDelFlag, "0")
                .one();

        if (tag == null) {
            throw new CustomException("标签不存在");
        }

        // 检查是否为当前用户的标签
        if (userId != null && !tag.getUserId().equals(userId)) {
            throw new CustomException("无权限查看该标签");
        }

        return tag;
    }
}
