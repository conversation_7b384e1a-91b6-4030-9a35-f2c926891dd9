package com.boryou.web.module.search.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

@Getter
@RequiredArgsConstructor
public enum ChangeEnum {

    RISK_GRADE("风险级别", 1, "1"),
    TAG("标签", 2, "2"),
    ;

    private static final Map<Integer, ChangeEnum> map = new HashMap<>();

    static {
        ChangeEnum[] ens = ChangeEnum.values();
        for (ChangeEnum en : ens) {
            map.put(en.getType(), en);
        }
    }

    private final String name;
    private final Integer type;
    /**
     * 1 表示 md5 查询 2 表示 indexId 查询
     */
    private final String queryType;

    public static ChangeEnum getEnumByType(Integer type) {
        return map.get(type);
    }

    public static Set<Integer> getTypes() {
        return map.keySet();
    }

}
