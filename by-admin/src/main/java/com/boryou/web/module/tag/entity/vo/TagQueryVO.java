package com.boryou.web.module.tag.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 标签查询VO类
 * 
 * <AUTHOR>
 */
@Data
public class TagQueryVO {

    /**
     * 标签内容（支持模糊查询）
     */
    private String tagContent;

    /**
     * 用户ID（如果不传则查询当前用户的标签）
     */
    private Long userId;

    /**
     * 删除标志（0代表存在 2代表删除）
     * 默认查询未删除的
     */
    private String delFlag = "0";

    /**
     * 创建时间范围 - 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTimeStart;

    /**
     * 创建时间范围 - 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTimeEnd;

    /**
     * 更新时间范围 - 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTimeStart;

    /**
     * 更新时间范围 - 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTimeEnd;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 排序字段（createTime, updateTime, tagContent）
     * 默认按更新时间倒序
     */
    private String orderBy = "updateTime";

    /**
     * 排序方向（asc, desc）
     * 默认倒序
     */
    private String orderDirection = "desc";

    /**
     * 分页参数 - 页码
     */
    private Integer pageNum;

    /**
     * 分页参数 - 每页大小
     */
    private Integer pageSize;
}
