package com.boryou.web.module.tag.controller;

import com.boryou.common.core.controller.BaseController;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.web.module.tag.entity.Tag;
import com.boryou.web.module.tag.entity.vo.TagVO;
import com.boryou.web.module.tag.entity.vo.TagQueryVO;
import com.boryou.web.module.tag.service.TagService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/tag")
@RequiredArgsConstructor
public class TagController {

    private final TagService tagService;

    /**
     * 新增或更新标签
     */
    @PostMapping("/saveOrUpdate")
    public AjaxResult saveOrUpdate(@Validated @RequestBody TagVO tagVO) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        tagService.saveOrUpdateTag(tagVO, user);
        return AjaxResult.success();
    }

    /**
     * 查询标签列表
     * 支持分页和多条件查询
     */
    @PostMapping("/list")
    public AjaxResult list(@RequestBody TagQueryVO queryVO) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Object result = tagService.queryTags(queryVO, user.getUserId());
        return AjaxResult.success(result);
    }

    /**
     * 根据ID列表获取标签详情（支持单个和批量）
     */
    @PostMapping("/getByIds")
    public AjaxResult getByIds(@RequestBody List<Long> ids) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Object result = tagService.getTagsByIds(ids, user.getUserId());
        return AjaxResult.success(result);
    }

    /**
     * 删除标签（支持单个和批量）
     */
    @PostMapping("/delete")
    public AjaxResult delete(@RequestBody List<Long> ids) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        tagService.deleteTags(ids, user);
        return AjaxResult.success("删除成功");
    }

}
