package com.boryou.web.module.tag.controller;

import com.boryou.common.core.controller.BaseController;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.web.module.tag.entity.Tag;
import com.boryou.web.module.tag.entity.vo.TagVO;
import com.boryou.web.module.tag.service.TagService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/tag")
@RequiredArgsConstructor
public class TagController extends BaseController {

    private final TagService tagService;

    /**
     * 新增或更新标签
     */
    @PostMapping("/saveOrUpdate")
    public AjaxResult saveOrUpdate(@Validated @RequestBody TagVO tagVO) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        tagService.saveOrUpdateTag(tagVO, user);
        return AjaxResult.success();
    }

    /**
     * 根据标签内容查询标签列表
     */
    @GetMapping("/queryByContent")
    public AjaxResult queryByContent(@RequestParam(required = false) String tagContent) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        List<Tag> tags = tagService.queryTagsByContent(tagContent, user.getUserId());
        return AjaxResult.success(tags);
    }

    /**
     * 查询当前用户的所有标签
     */
    @GetMapping("/queryMyTags")
    public AjaxResult queryMyTags() {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        List<Tag> tags = tagService.queryTagsByUserId(user.getUserId());
        return AjaxResult.success(tags);
    }

    /**
     * 根据ID获取标签详情
     */
    @GetMapping("/{id}")
    public AjaxResult getById(@PathVariable Long id) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Tag tag = tagService.getTagById(id, user.getUserId());
        return AjaxResult.success(tag);
    }

    /**
     * 逻辑删除标签
     */
    @DeleteMapping("/{id}")
    public AjaxResult delete(@PathVariable Long id) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        tagService.deleteTag(id, user);
        return AjaxResult.success("删除成功");
    }

    /**
     * 批量逻辑删除标签
     */
    @DeleteMapping("/batch")
    public AjaxResult batchDelete(@RequestBody List<Long> ids) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        tagService.batchDeleteTags(ids, user);
        return AjaxResult.success("批量删除成功");
    }

}
