package com.boryou.web.module.warn.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.boryou.web.handle.ContactVOTypeHandler;
import com.boryou.web.module.warn.domain.vo.ContactVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 'warn_set' 表的实体类
 * 使用 Lombok 注解生成样板代码
 */
@Data  // 生成 getter, setter, toString, equals 和 hashCode 方法
@NoArgsConstructor  // 生成无参构造方法
@TableName(value = "by_warn_set", autoResultMap = true)  // 指定数据库中的表名
public class WarnSet {

    /**
     * 表标识
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 关联的板块方案id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @NotNull(message = "方案id不能为空")
    private Long planId;

    /**
     * 修改用户
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long deptId;

    /**
     * 接收时间
     */
    @NotNull(message = "接收时间不能为空")
    private Integer acceptTime;

    /**
     * 预警方式
     */
    @NotNull(message = "预警方式不能为空")
    private Integer warningType;

    /**
     * 预警间隔时间
     */
    @NotNull(message = "预警间隔时间不能为空")
    private Integer intervalTime;

    /**
     * 开始接收时段
     */
    private Integer startTime;

    /**
     * 结束接收时段
     */
    private Integer endTime;

    /**
     * 短信推送
     */
    private Integer message;


    private Integer mail;

    /**
     * 微信推送
     */
    private Integer chat;

    /**
     * 系统推送
     */
    private Integer system;

    /**
     * 短信接收用户id
     */
    @TableField(typeHandler = ContactVOTypeHandler.class, updateStrategy = FieldStrategy.IGNORED)
    private List<ContactVO> messageUser;

    /**
     * 邮箱接收用户
     */
    @TableField(typeHandler = ContactVOTypeHandler.class, updateStrategy = FieldStrategy.IGNORED)
    private List<ContactVO> mailUser;

    /**
     * 微信接收用户
     */
    @TableField(typeHandler = ContactVOTypeHandler.class, updateStrategy = FieldStrategy.IGNORED)
    private List<ContactVO> chatUser;

    /**
     * 最近一次的推送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastPushTime;

    /**
     * 最后一次推送id
     */
    private String lastPushId;

    /**
     * 状态 0关闭 1启用
     */
    @NotNull(message = "状态不能为空")
    private Integer status;

    /**
     * 媒体类型
     */
    @NotBlank(message = "媒体类型不能为空")
    private String type;

    /**
     * 信息属性
     */
    @NotBlank(message = "信息属性不能为空")
    private String emotionFlag;

    /**
     * 匹配方式
     */
    @NotBlank(message = "匹配方式不能为空")
    private String searchPosition;

    /**
     * 是否去重
     */
    @NotBlank(message = "是否去重不能为空")
    private String isOriginal;

    /**
     * 预警词
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String kw1;

    /**
     * 排除词
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String excludeWord;

    /**
     * 预警分级设置
     */
    @NotEmpty(message = "预警分级设置不能为空")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Integer> grading;

    /**
     * 相似信息 > XX 条
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer similarInfo;
}
