package com.boryou.web.module.search.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.exception.CustomException;
import com.boryou.web.module.search.entity.ChangeData;
import com.boryou.web.module.search.entity.vo.ChangeDataVO;
import com.boryou.web.module.search.enums.ChangeEnum;
import com.boryou.web.module.search.mapper.ChangeDataMapper;
import com.boryou.web.module.tag.entity.Tag;
import com.boryou.web.module.tag.entity.vo.TagSimpleVO;
import com.boryou.web.module.tag.service.TagService;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class ChangeDataService extends ServiceImpl<ChangeDataMapper, ChangeData> {

    private final TagService tagService;

    public void updateChangeData(ChangeDataVO changeDataVO, SysUser user) {
        Long indexId = changeDataVO.getIndexId();
        String md5 = changeDataVO.getMd5();
        Integer changeType = changeDataVO.getChangeType();
        String changeValue = changeDataVO.getChangeValue();

        ChangeEnum enumByType = ChangeEnum.getEnumByType(changeType);
        if (enumByType == null) {
            throw new CustomException("changeType不存在");
        }

        String queryType = enumByType.getQueryType();
        if (Objects.equals(queryType, "1")) {
            // md5查询时indexId存储空
            indexId = null;
        }

        DateTime date = DateUtil.date();

        Long userId = user.getUserId();
        String userName = user.getUserName();

        ChangeData one = this.lambdaQuery()
                .eq(indexId != null, ChangeData::getIndexId, indexId)
                .eq(ChangeData::getMd5, md5)
                .eq(ChangeData::getChangeType, changeType)
                .eq(ChangeData::getUserId, userId)
                .one();

        if (one == null) {
            // 新增
            ChangeData changeData = new ChangeData();
            changeData.setId(IdUtil.getSnowflakeNextId());
            changeData.setMd5(md5);
            changeData.setIndexId(indexId);
            changeData.setUserId(userId);
            changeData.setChangeType(changeType);
            changeData.setChangeValue(changeValue);
            changeData.setCreateBy(userName);
            changeData.setCreateTime(date);
            changeData.setUpdateBy(userName);
            changeData.setUpdateTime(date);
            this.save(changeData);
            return;
        }
        String changeValueSql = one.getChangeValue();
        if (changeValue.equals(changeValueSql)) {
            // 未修改
            return;
        }
        // 修改
        one.setChangeValue(changeValue);
        one.setUpdateBy(userName);
        one.setUpdateTime(date);
        this.updateById(one);
    }

    /**
     * 根据批量indexId和md5Id查询变更数据
     *
     * @param indexIds 索引ID列表
     * @param md5Ids   MD5 ID列表
     * @param userId   用户ID
     * @return Guava Table，行key为查询标识(indexId或md5)，列key为changeType，value为处理后的值
     */
    public Table<String, Integer, Object> getBatchChangeDataTable(List<String> indexIds, List<String> md5Ids, Long userId) {
        Table<String, Integer, Object> resultTable = HashBasedTable.create();

        if (userId == null) {
            return resultTable;
        }

        // 查询符合条件的ChangeData记录
        List<ChangeData> changeDataList = this.lambdaQuery()
                .and(wrapper -> {
                    // 根据indexIds查询（如果有）
                    if (CollUtil.isNotEmpty(indexIds)) {
                        wrapper.in(ChangeData::getIndexId, indexIds);
                    }
                    // 根据md5Ids查询（如果有）
                    if (CollUtil.isNotEmpty(md5Ids)) {
                        if (CollUtil.isNotEmpty(indexIds)) {
                            wrapper.or().in(ChangeData::getMd5, md5Ids);
                        } else {
                            wrapper.in(ChangeData::getMd5, md5Ids);
                        }
                    }
                })
                .eq(ChangeData::getUserId, userId)
                .list();

        if (CollUtil.isEmpty(changeDataList)) {
            return resultTable;
        }

        // 按 changeType 分组处理
        Map<Integer, List<ChangeData>> changeTypeGroups = changeDataList.stream()
                .collect(Collectors.groupingBy(ChangeData::getChangeType));

        // 处理每种 changeType
        changeTypeGroups.forEach((changeType, dataList) -> {
            ChangeEnum changeEnum = ChangeEnum.getEnumByType(changeType);
            if (changeEnum == null) {
                return;
            }

            if (ChangeEnum.TAG.equals(changeEnum)) {
                // TAG 类型特殊处理：需要根据 changeValue（tag id）查询标签信息，并按 indexId 聚合
                processTagChangeData(dataList, resultTable, userId);
            } else {
                // 其他类型（如 RISK_GRADE）直接处理
                processNormalChangeData(dataList, resultTable, changeEnum);
            }
        });

        return resultTable;
    }

    /**
     * 处理TAG类型的变更数据
     * TAG需要根据changeValue（tag id）查询标签信息，并按indexId聚合
     */
    private void processTagChangeData(List<ChangeData> dataList, Table<String, Integer, Object> resultTable, Long userId) {
        // 按 indexId 分组
        Map<Long, List<ChangeData>> indexGroups = dataList.stream()
                .collect(Collectors.groupingBy(ChangeData::getIndexId));

        // 收集所有的 tag id
        Set<Long> tagIds = dataList.stream()
                .map(ChangeData::getChangeValue)
                .filter(StrUtil::isNotBlank)
                .map(Long::valueOf)
                .collect(Collectors.toSet());

        if (CollUtil.isEmpty(tagIds)) {
            return;
        }

        // 批量查询标签信息
        Map<Long, Tag> tagMap = tagService.lambdaQuery()
                .in(Tag::getId, tagIds)
                .eq(Tag::getUserId, userId)
                .eq(Tag::getDelFlag, "0")
                .list()
                .stream()
                .collect(Collectors.toMap(Tag::getId, Function.identity()));

        // 为每个 indexId 组装标签列表
        indexGroups.forEach((indexId, indexDataList) -> {
            List<TagSimpleVO> tagList = indexDataList.stream()
                    .map(ChangeData::getChangeValue)
                    .filter(StrUtil::isNotBlank)
                    .map(Long::valueOf)
                    .map(tagMap::get)
                    .filter(Objects::nonNull)
                    .map(tag -> {
                        TagSimpleVO vo = new TagSimpleVO();
                        vo.setId(tag.getId());
                        vo.setTagContent(tag.getTagContent());
                        return vo;
                    })
                    .collect(Collectors.toList());

            if (CollUtil.isNotEmpty(tagList)) {
                resultTable.put(String.valueOf(indexId), ChangeEnum.TAG.getType(), tagList);
            }
        });
    }

    /**
     * 处理普通类型的变更数据（如风险级别）
     */
    private void processNormalChangeData(List<ChangeData> dataList, Table<String, Integer, Object> resultTable, ChangeEnum changeEnum) {
        for (ChangeData changeData : dataList) {
            String changeValue = changeData.getChangeValue();

            String rowKey;
            if ("1".equals(changeEnum.getQueryType())) {
                // queryType = "1" 表示使用 md5 查询
                rowKey = changeData.getMd5();
            } else if ("2".equals(changeEnum.getQueryType())) {
                // queryType = "2" 表示使用 indexId 查询
                rowKey = String.valueOf(changeData.getIndexId());
            } else {
                // 默认使用 md5
                rowKey = changeData.getMd5();
            }

            if (rowKey != null && StrUtil.isNotBlank(changeValue)) {
                resultTable.put(rowKey, changeEnum.getType(), changeValue);
            }
        }
    }

}
