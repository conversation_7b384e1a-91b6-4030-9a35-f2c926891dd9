package com.boryou.web.module.search.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.exception.CustomException;
import com.boryou.web.module.search.entity.ChangeData;
import com.boryou.web.module.search.entity.vo.ChangeDataVO;
import com.boryou.web.module.search.enums.ChangeEnum;
import com.boryou.web.module.search.mapper.ChangeDataMapper;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class ChangeDataService extends ServiceImpl<ChangeDataMapper, ChangeData> {

    public void updateChangeData(ChangeDataVO changeDataVO, SysUser user) {
        Long indexId = changeDataVO.getIndexId();
        String md5 = changeDataVO.getMd5();
        Integer changeType = changeDataVO.getChangeType();
        String changeValue = changeDataVO.getChangeValue();

        ChangeEnum enumByType = ChangeEnum.getEnumByType(changeType);
        if (enumByType == null) {
            throw new CustomException("changeType不存在");
        }

        String queryType = enumByType.getQueryType();
        if (Objects.equals(queryType, "1")) {
            // md5查询时indexId存储空
            indexId = null;
        }

        DateTime date = DateUtil.date();

        Long userId = user.getUserId();
        String userName = user.getUserName();

        ChangeData one = this.lambdaQuery()
                .eq(indexId != null, ChangeData::getIndexId, indexId)
                .eq(ChangeData::getMd5, md5)
                .eq(ChangeData::getChangeType, changeType)
                .eq(ChangeData::getUserId, userId)
                .one();

        if (one == null) {
            // 新增
            ChangeData changeData = new ChangeData();
            changeData.setId(IdUtil.getSnowflakeNextId());
            changeData.setMd5(md5);
            changeData.setIndexId(indexId);
            changeData.setUserId(userId);
            changeData.setChangeType(changeType);
            changeData.setChangeValue(changeValue);
            changeData.setCreateBy(userName);
            changeData.setCreateTime(date);
            changeData.setUpdateBy(userName);
            changeData.setUpdateTime(date);
            this.save(changeData);
            return;
        }
        String changeValueSql = one.getChangeValue();
        if (changeValue.equals(changeValueSql)) {
            // 未修改
            return;
        }
        // 修改
        one.setChangeValue(changeValue);
        one.setUpdateBy(userName);
        one.setUpdateTime(date);
        this.updateById(one);
    }

    /**
     * 根据批量indexId和userId查询变更数据
     *
     * @param indexIds 索引ID列表
     * @param userId   用户ID
     * @return Guava Table，行key为indexId，列key为changeType，value为changeValue
     */
    public Table<String, Integer, Object> getBatchChangeDataTable(List<String> md5Ids, Long userId) {
        Table<String, Integer, Object> resultTable = HashBasedTable.create();
        if (CollUtil.isEmpty(md5Ids) || userId == null) {
            return resultTable;
        }

        // 查询符合条件的ChangeData记录
        List<ChangeData> changeDataList = this.lambdaQuery()
                .in(ChangeData::getMd5, md5Ids)
                .eq(ChangeData::getUserId, userId)
                .list();

        if (CollUtil.isEmpty(changeDataList)) {
            return resultTable;
        }

        // 使用 Guava Table 存储双key数据
        for (ChangeData changeData : changeDataList) {
            String md5 = changeData.getMd5();
            Integer changeType = changeData.getChangeType();
            String changeValue = changeData.getChangeValue();

            resultTable.put(md5, changeType, changeValue);
        }

        return resultTable;
    }

}
