# 标签管理API快速参考

## 🚀 接口列表

| 接口 | 方法 | 路径 | 功能 |
|------|------|------|------|
| 查询列表 | POST | `/tag/list` | 多条件查询、分页、排序 |
| 新增更新 | POST | `/tag/saveOrUpdate` | 新增或更新标签 |
| 获取详情 | POST | `/tag/getByIds` | 单个/批量获取详情 |
| 删除标签 | POST | `/tag/delete` | 单个/批量删除 |

## 📝 快速示例

### 查询所有标签
```json
POST /tag/list
{}
```

### 分页查询
```json
POST /tag/list
{
    "pageNum": 1,
    "pageSize": 10
}
```

### 模糊查询
```json
POST /tag/list
{
    "tagContent": "工作"
}
```

### 新增标签
```json
POST /tag/saveOrUpdate
{
    "tagContent": "重要工作"
}
```

### 更新标签
```json
POST /tag/saveOrUpdate
{
    "id": 123456789,
    "tagContent": "更新后的内容"
}
```

### 获取单个标签
```json
POST /tag/getByIds
[123456789]
```

### 批量获取标签
```json
POST /tag/getByIds
[123456789, 987654321]
```

### 删除单个标签
```json
POST /tag/delete
[123456789]
```

### 批量删除标签
```json
POST /tag/delete
[123456789, 987654321, 555666777]
```

## 🎯 核心特性

- ✅ **统一POST**: 所有接口使用POST方法
- ✅ **智能批量**: 单个和批量操作统一接口
- ✅ **参数扩展**: 查询条件可无限扩展
- ✅ **权限隔离**: 用户只能操作自己的标签
- ✅ **逻辑删除**: 数据安全，可恢复

## 📋 常用查询条件

```json
{
    "tagContent": "关键词",      // 模糊查询
    "delFlag": "0",             // 0-存在, 2-删除
    "createBy": "admin",        // 创建者
    "orderBy": "updateTime",    // 排序字段
    "orderDirection": "desc",   // 排序方向
    "pageNum": 1,               // 页码
    "pageSize": 10              // 每页大小
}
```

## 🔧 前端封装示例

```javascript
class TagAPI {
    static async list(params = {}) {
        return this.post('/tag/list', params);
    }

    static async save(tagContent) {
        return this.post('/tag/saveOrUpdate', { tagContent });
    }

    static async update(id, tagContent) {
        return this.post('/tag/saveOrUpdate', { id, tagContent });
    }

    static async getByIds(ids) {
        return this.post('/tag/getByIds', Array.isArray(ids) ? ids : [ids]);
    }

    static async delete(ids) {
        return this.post('/tag/delete', Array.isArray(ids) ? ids : [ids]);
    }

    static async post(url, data) {
        const response = await fetch(url, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
        });
        return response.json();
    }
}

// 使用示例
TagAPI.list({ tagContent: '工作', pageSize: 10 });
TagAPI.save('新标签');                    // 新增
TagAPI.update(123456789, '更新后的内容');  // 更新
TagAPI.getByIds([123, 456]);
TagAPI.delete([123, 456]);
```
