# ChangeDataService.getBatchChangeDataTable 测试用例

## 🧪 测试数据准备

### 测试数据库记录
```sql
-- 用户1001的测试数据
INSERT INTO by_change_data VALUES 
-- 风险级别数据 (changeType=1, queryType="1", 使用md5)
(1, 1001, 1001, 'abc123', 1, '高风险', 'admin', '2024-01-01 10:00:00', 'admin', '2024-01-01 10:00:00'),
(2, 1002, 1001, 'def456', 1, '中风险', 'admin', '2024-01-01 11:00:00', 'admin', '2024-01-01 11:00:00'),
(3, 1003, 1001, 'ghi789', 1, '低风险', 'admin', '2024-01-01 12:00:00', 'admin', '2024-01-01 12:00:00'),

-- 标签数据 (changeType=2, queryType="2", 使用indexId)
(4, 2001, 1001, 'tag001', 2, '重要标签', 'admin', '2024-01-01 13:00:00', 'admin', '2024-01-01 13:00:00'),
(5, 2002, 1001, 'tag002', 2, '普通标签', 'admin', '2024-01-01 14:00:00', 'admin', '2024-01-01 14:00:00'),
(6, 2003, 1001, 'tag003', 2, '临时标签', 'admin', '2024-01-01 15:00:00', 'admin', '2024-01-01 15:00:00');

-- 用户1002的测试数据（用于权限测试）
INSERT INTO by_change_data VALUES 
(7, 3001, 1002, 'xyz999', 1, '其他用户风险', 'user2', '2024-01-01 16:00:00', 'user2', '2024-01-01 16:00:00');
```

## 🔍 测试用例

### 测试用例1：只查询 md5Ids
```java
@Test
public void testQueryByMd5IdsOnly() {
    // 准备数据
    List<String> md5Ids = Arrays.asList("abc123", "def456", "ghi789");
    Long userId = 1001L;
    
    // 执行查询
    Table<String, Integer, String> result = changeDataService.getBatchChangeDataTable(null, md5Ids, userId);
    
    // 验证结果
    assertEquals("高风险", result.get("abc123", 1));
    assertEquals("中风险", result.get("def456", 1));
    assertEquals("低风险", result.get("ghi789", 1));
    
    // 验证行数
    assertEquals(3, result.rowKeySet().size());
    
    // 验证不存在的数据
    assertNull(result.get("notexist", 1));
}
```

### 测试用例2：只查询 indexIds
```java
@Test
public void testQueryByIndexIdsOnly() {
    // 准备数据
    List<String> indexIds = Arrays.asList("2001", "2002", "2003");
    Long userId = 1001L;
    
    // 执行查询
    Table<String, Integer, String> result = changeDataService.getBatchChangeDataTable(indexIds, null, userId);
    
    // 验证结果
    assertEquals("重要标签", result.get("2001", 2));
    assertEquals("普通标签", result.get("2002", 2));
    assertEquals("临时标签", result.get("2003", 2));
    
    // 验证行数
    assertEquals(3, result.rowKeySet().size());
}
```

### 测试用例3：同时查询 indexIds 和 md5Ids
```java
@Test
public void testQueryByBothIds() {
    // 准备数据
    List<String> indexIds = Arrays.asList("2001", "2002");
    List<String> md5Ids = Arrays.asList("abc123", "def456");
    Long userId = 1001L;
    
    // 执行查询
    Table<String, Integer, String> result = changeDataService.getBatchChangeDataTable(indexIds, md5Ids, userId);
    
    // 验证风险级别数据（使用md5作为key）
    assertEquals("高风险", result.get("abc123", 1));
    assertEquals("中风险", result.get("def456", 1));
    
    // 验证标签数据（使用indexId作为key）
    assertEquals("重要标签", result.get("2001", 2));
    assertEquals("普通标签", result.get("2002", 2));
    
    // 验证总行数
    assertEquals(4, result.rowKeySet().size());
}
```

### 测试用例4：空参数测试
```java
@Test
public void testEmptyParameters() {
    Long userId = 1001L;
    
    // 测试空的indexIds和md5Ids
    Table<String, Integer, String> result1 = changeDataService.getBatchChangeDataTable(null, null, userId);
    assertTrue(result1.isEmpty());
    
    // 测试空列表
    Table<String, Integer, String> result2 = changeDataService.getBatchChangeDataTable(
        Collections.emptyList(), Collections.emptyList(), userId);
    assertTrue(result2.isEmpty());
    
    // 测试null userId
    List<String> md5Ids = Arrays.asList("abc123");
    Table<String, Integer, String> result3 = changeDataService.getBatchChangeDataTable(null, md5Ids, null);
    assertTrue(result3.isEmpty());
}
```

### 测试用例5：权限隔离测试
```java
@Test
public void testUserPermissionIsolation() {
    // 准备数据
    List<String> md5Ids = Arrays.asList("abc123", "xyz999"); // xyz999属于用户1002
    Long userId = 1001L; // 查询用户1001的数据
    
    // 执行查询
    Table<String, Integer, String> result = changeDataService.getBatchChangeDataTable(null, md5Ids, userId);
    
    // 验证只能查到用户1001的数据
    assertEquals("高风险", result.get("abc123", 1));
    assertNull(result.get("xyz999", 1)); // 不能查到其他用户的数据
    
    // 验证行数
    assertEquals(1, result.rowKeySet().size());
}
```

### 测试用例6：不存在的ID测试
```java
@Test
public void testNonExistentIds() {
    // 准备数据
    List<String> indexIds = Arrays.asList("9999", "8888");
    List<String> md5Ids = Arrays.asList("notexist1", "notexist2");
    Long userId = 1001L;
    
    // 执行查询
    Table<String, Integer, String> result = changeDataService.getBatchChangeDataTable(indexIds, md5Ids, userId);
    
    // 验证结果为空
    assertTrue(result.isEmpty());
}
```

### 测试用例7：混合存在和不存在的ID
```java
@Test
public void testMixedExistentAndNonExistentIds() {
    // 准备数据
    List<String> indexIds = Arrays.asList("2001", "9999"); // 2001存在，9999不存在
    List<String> md5Ids = Arrays.asList("abc123", "notexist"); // abc123存在，notexist不存在
    Long userId = 1001L;
    
    // 执行查询
    Table<String, Integer, String> result = changeDataService.getBatchChangeDataTable(indexIds, md5Ids, userId);
    
    // 验证只返回存在的数据
    assertEquals("重要标签", result.get("2001", 2));
    assertEquals("高风险", result.get("abc123", 1));
    
    // 验证不存在的数据
    assertNull(result.get("9999", 2));
    assertNull(result.get("notexist", 1));
    
    // 验证行数
    assertEquals(2, result.rowKeySet().size());
}
```

## 📊 预期结果验证

### 结果结构验证
```java
@Test
public void testResultStructure() {
    List<String> indexIds = Arrays.asList("2001");
    List<String> md5Ids = Arrays.asList("abc123");
    Long userId = 1001L;
    
    Table<String, Integer, String> result = changeDataService.getBatchChangeDataTable(indexIds, md5Ids, userId);
    
    // 验证Table结构
    assertNotNull(result);
    assertTrue(result instanceof Table);
    
    // 验证行key类型
    for (String rowKey : result.rowKeySet()) {
        assertTrue(rowKey instanceof String);
    }
    
    // 验证列key类型
    for (Integer columnKey : result.columnKeySet()) {
        assertTrue(columnKey instanceof Integer);
    }
    
    // 验证值类型
    for (String value : result.values()) {
        assertTrue(value instanceof String);
    }
}
```

## 🎯 性能测试

### 大批量数据测试
```java
@Test
public void testLargeBatchQuery() {
    // 准备大量ID
    List<String> indexIds = new ArrayList<>();
    List<String> md5Ids = new ArrayList<>();
    
    for (int i = 1; i <= 1000; i++) {
        indexIds.add(String.valueOf(2000 + i));
        md5Ids.add("md5_" + i);
    }
    
    Long userId = 1001L;
    
    // 执行查询并测量时间
    long startTime = System.currentTimeMillis();
    Table<String, Integer, String> result = changeDataService.getBatchChangeDataTable(indexIds, md5Ids, userId);
    long endTime = System.currentTimeMillis();
    
    // 验证性能（根据实际情况调整阈值）
    assertTrue("查询时间应该在合理范围内", (endTime - startTime) < 5000); // 5秒内
    
    // 验证结果
    assertNotNull(result);
}
```

## 🔧 Mock测试示例

```java
@Test
public void testWithMockData() {
    // 使用Mockito模拟数据
    ChangeDataService mockService = Mockito.mock(ChangeDataService.class);
    
    // 准备模拟返回数据
    Table<String, Integer, String> mockTable = HashBasedTable.create();
    mockTable.put("abc123", 1, "高风险");
    mockTable.put("2001", 2, "重要标签");
    
    // 设置模拟行为
    when(mockService.getBatchChangeDataTable(any(), any(), any())).thenReturn(mockTable);
    
    // 执行测试
    List<String> indexIds = Arrays.asList("2001");
    List<String> md5Ids = Arrays.asList("abc123");
    Table<String, Integer, String> result = mockService.getBatchChangeDataTable(indexIds, md5Ids, 1001L);
    
    // 验证结果
    assertEquals("高风险", result.get("abc123", 1));
    assertEquals("重要标签", result.get("2001", 2));
}
```
