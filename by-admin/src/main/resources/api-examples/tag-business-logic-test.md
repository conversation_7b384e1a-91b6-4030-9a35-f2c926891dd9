# 标签管理业务逻辑测试用例

## 🧪 saveOrUpdate 接口测试用例

### 测试场景1：新增标签（不传ID）

#### 1.1 正常新增
**请求**：
```json
POST /tag/saveOrUpdate
{
    "tagContent": "工作标签"
}
```
**预期结果**：成功创建标签，delFlag="0"

#### 1.2 新增重复内容
**前置条件**：用户已有内容为"工作标签"的未删除标签
**请求**：
```json
POST /tag/saveOrUpdate
{
    "tagContent": "工作标签"
}
```
**预期结果**：抛出异常"标签内容已存在"

#### 1.3 新增空内容
**请求**：
```json
POST /tag/saveOrUpdate
{
    "tagContent": ""
}
```
**预期结果**：抛出异常"标签内容不能为空"

### 测试场景2：更新标签（传入ID）

#### 2.1 正常更新
**前置条件**：用户有ID为123的标签，内容为"旧内容"，delFlag="0"
**请求**：
```json
POST /tag/saveOrUpdate
{
    "id": 123,
    "tagContent": "新内容"
}
```
**预期结果**：成功更新标签内容和时间

#### 2.2 更新为相同内容
**前置条件**：用户有ID为123的标签，内容为"相同内容"
**请求**：
```json
POST /tag/saveOrUpdate
{
    "id": 123,
    "tagContent": "相同内容"
}
```
**预期结果**：直接返回，不做任何更新

#### 2.3 更新为重复内容
**前置条件**：
- 用户有ID为123的标签，内容为"标签A"
- 用户有ID为456的标签，内容为"标签B"
**请求**：
```json
POST /tag/saveOrUpdate
{
    "id": 123,
    "tagContent": "标签B"
}
```
**预期结果**：抛出异常"标签内容已存在"

#### 2.4 更新不存在的标签
**请求**：
```json
POST /tag/saveOrUpdate
{
    "id": 999,
    "tagContent": "新内容"
}
```
**预期结果**：抛出异常"标签不存在"

#### 2.5 更新其他用户的标签
**前置条件**：ID为123的标签属于其他用户
**请求**：
```json
POST /tag/saveOrUpdate
{
    "id": 123,
    "tagContent": "新内容"
}
```
**预期结果**：抛出异常"无权限操作该标签"

#### 2.6 恢复已删除的标签
**前置条件**：用户有ID为123的标签，delFlag="2"（已删除）
**请求**：
```json
POST /tag/saveOrUpdate
{
    "id": 123,
    "tagContent": "恢复内容"
}
```
**预期结果**：成功更新内容并设置delFlag="0"

#### 2.7 恢复已删除标签但内容重复
**前置条件**：
- 用户有ID为123的标签，delFlag="2"，内容为"删除标签"
- 用户有ID为456的标签，delFlag="0"，内容为"存在标签"
**请求**：
```json
POST /tag/saveOrUpdate
{
    "id": 123,
    "tagContent": "存在标签"
}
```
**预期结果**：抛出异常"标签内容已存在"

## 🔍 业务逻辑验证点

### 新增模式验证点
1. ✅ 内容不能为空
2. ✅ 不能与当前用户的未删除标签重复
3. ✅ 自动设置用户ID、创建时间、删除标志等字段
4. ✅ 使用雪花算法生成ID

### 更新模式验证点
1. ✅ 标签必须存在
2. ✅ 只能操作自己的标签
3. ✅ 内容无变化时不执行更新
4. ✅ 新内容不能与其他标签重复（排除自己）
5. ✅ 已删除标签可以恢复
6. ✅ 更新时间和更新人字段

### 权限控制验证点
1. ✅ 用户隔离：只能操作自己的标签
2. ✅ 删除状态处理：正确处理删除标志
3. ✅ 重复性检查：避免内容重复

## 📊 测试数据准备

### 用户A的测试数据
```sql
INSERT INTO by_tag VALUES 
(1, 1001, '工作标签', 'userA', '2024-01-01 10:00:00', 'userA', '2024-01-01 10:00:00', '0'),
(2, 1001, '学习标签', 'userA', '2024-01-01 11:00:00', 'userA', '2024-01-01 11:00:00', '0'),
(3, 1001, '删除标签', 'userA', '2024-01-01 12:00:00', 'userA', '2024-01-01 12:00:00', '2');
```

### 用户B的测试数据
```sql
INSERT INTO by_tag VALUES 
(4, 1002, '工作标签', 'userB', '2024-01-01 10:00:00', 'userB', '2024-01-01 10:00:00', '0'),
(5, 1002, '生活标签', 'userB', '2024-01-01 11:00:00', 'userB', '2024-01-01 11:00:00', '0');
```

## 🎯 预期行为总结

| 场景 | ID | 内容 | 用户权限 | 删除状态 | 重复检查 | 预期结果 |
|------|----|----|----------|----------|----------|----------|
| 新增 | 无 | 新内容 | ✅ | - | 无重复 | 成功创建 |
| 新增 | 无 | 重复内容 | ✅ | - | 有重复 | 抛出异常 |
| 更新 | 有 | 新内容 | ✅ | 正常 | 无重复 | 成功更新 |
| 更新 | 有 | 相同内容 | ✅ | 正常 | - | 直接返回 |
| 更新 | 有 | 重复内容 | ✅ | 正常 | 有重复 | 抛出异常 |
| 更新 | 有 | 新内容 | ❌ | 正常 | - | 权限异常 |
| 更新 | 有 | 新内容 | ✅ | 已删除 | 无重复 | 恢复并更新 |
| 更新 | 无效 | 任意 | ✅ | - | - | 不存在异常 |
