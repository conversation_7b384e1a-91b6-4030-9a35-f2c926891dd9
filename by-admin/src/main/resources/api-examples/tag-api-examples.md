# 标签管理API使用示例

## API接口总览

| 方法 | 路径 | 功能 | 说明 |
|------|------|------|------|
| GET | `/tag/list` | 查询标签列表 | 支持多条件查询、分页、排序 |
| POST | `/tag/saveOrUpdate` | 新增或更新标签 | 相同内容则更新时间 |
| GET | `/tag/{id}` | 获取标签详情 | 根据ID获取单个标签 |
| DELETE | `/tag/{ids}` | 删除标签 | 支持单个和批量删除 |

## 1. 查询标签列表 - GET /tag/list

### 1.1 查询当前用户的所有标签
```
GET /tag/list
```

### 1.2 按标签内容模糊查询
```
GET /tag/list?tagContent=工作
```

### 1.3 分页查询
```
GET /tag/list?pageNum=1&pageSize=10
```

### 1.4 按时间范围查询
```
GET /tag/list?createTimeStart=2024-01-01 00:00:00&createTimeEnd=2024-12-31 23:59:59
```

### 1.5 复合查询（标签内容 + 分页 + 排序）
```
GET /tag/list?tagContent=重要&pageNum=1&pageSize=5&orderBy=createTime&orderDirection=desc
```

### 1.6 查询已删除的标签
```
GET /tag/list?delFlag=2
```

### 1.7 按创建者查询
```
GET /tag/list?createBy=admin
```

## 2. 新增或更新标签 - POST /tag/saveOrUpdate

### 2.1 新增标签
```json
POST /tag/saveOrUpdate
Content-Type: application/json

{
    "tagContent": "重要工作"
}
```

### 2.2 更新标签（如果标签内容已存在则更新时间）
```json
POST /tag/saveOrUpdate
Content-Type: application/json

{
    "tagContent": "重要工作"
}
```

## 3. 获取标签详情 - GET /tag/{id}

```
GET /tag/123456789
```

## 4. 删除标签 - DELETE /tag/{ids}

### 4.1 单个删除
```
DELETE /tag/123456789
```

### 4.2 批量删除（逗号分隔）
```
DELETE /tag/123456789,987654321,555666777
```

### 4.3 批量删除（带空格也支持）
```
DELETE /tag/123456789, 987654321, 555666777
```

**删除接口说明**：
- 支持单个ID和批量ID（逗号分隔）
- 自动过滤空白字符
- 会验证所有ID是否存在
- 只能删除当前用户的标签
- 已删除的标签不能重复删除
- 采用逻辑删除，数据不会物理删除

## 查询参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| tagContent | String | 否 | 标签内容，支持模糊查询 |
| userId | Long | 否 | 用户ID，不传则查询当前用户 |
| delFlag | String | 否 | 删除标志，0-存在，2-删除，默认0 |
| createTimeStart | Date | 否 | 创建时间范围开始 |
| createTimeEnd | Date | 否 | 创建时间范围结束 |
| updateTimeStart | Date | 否 | 更新时间范围开始 |
| updateTimeEnd | Date | 否 | 更新时间范围结束 |
| createBy | String | 否 | 创建者，支持模糊查询 |
| orderBy | String | 否 | 排序字段：createTime/updateTime/tagContent，默认updateTime |
| orderDirection | String | 否 | 排序方向：asc/desc，默认desc |
| pageNum | Integer | 否 | 页码，传入则启用分页 |
| pageSize | Integer | 否 | 每页大小，传入则启用分页 |

## 响应格式

### 不分页响应
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": [
        {
            "id": "123456789",
            "userId": "1001",
            "tagContent": "重要工作",
            "createBy": "admin",
            "createTime": "2024-01-15 10:30:00",
            "updateBy": "admin",
            "updateTime": "2024-01-15 10:30:00",
            "delFlag": "0"
        }
    ]
}
```

### 分页响应
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "records": [
            {
                "id": "123456789",
                "userId": "1001",
                "tagContent": "重要工作",
                "createBy": "admin",
                "createTime": "2024-01-15 10:30:00",
                "updateBy": "admin",
                "updateTime": "2024-01-15 10:30:00",
                "delFlag": "0"
            }
        ],
        "total": 100,
        "size": 10,
        "current": 1,
        "pages": 10
    }
}
```
