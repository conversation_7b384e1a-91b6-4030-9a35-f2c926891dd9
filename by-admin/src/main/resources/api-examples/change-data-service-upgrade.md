# ChangeDataService.getBatchChangeDataTable 方法改造说明

## 🔄 改造内容

### 原方法签名
```java
public Table<String, Integer, Object> getBatchChangeDataTable(List<String> md5Ids, Long userId)
```

### 新方法签名
```java
public Table<String, Integer, String> getBatchChangeDataTable(List<String> indexIds, List<String> md5Ids, Long userId)
```

## 🎯 改造目标

1. **支持双重查询条件**：同时支持 indexId 和 md5Id 批量查询
2. **智能字段选择**：根据 ChangeEnum 的 queryType 决定返回结果的行key
3. **兼容现有逻辑**：保持与后续获取逻辑的兼容性

## 📊 ChangeEnum 配置说明

根据 ChangeEnum 的定义：

| 枚举值 | 名称 | type | queryType | 说明 |
|--------|------|------|-----------|------|
| RISK_GRADE | 风险级别 | 1 | "1" | 使用 md5 查询 |
| TAG | 标签 | 2 | "2" | 使用 indexId 查询 |

### queryType 含义
- `"1"` - 表示使用 md5 查询，返回结果以 md5 作为行key
- `"2"` - 表示使用 indexId 查询，返回结果以 indexId 作为行key

## 🔧 核心逻辑

### 1. 查询逻辑
```java
// 支持 indexIds 和 md5Ids 的联合查询
List<ChangeData> changeDataList = this.lambdaQuery()
    .and(wrapper -> {
        // 根据indexIds查询（如果有）
        if (CollUtil.isNotEmpty(indexIds)) {
            wrapper.in(ChangeData::getIndexId, indexIds);
        }
        // 根据md5Ids查询（如果有）
        if (CollUtil.isNotEmpty(md5Ids)) {
            if (CollUtil.isNotEmpty(indexIds)) {
                wrapper.or().in(ChangeData::getMd5, md5Ids);
            } else {
                wrapper.in(ChangeData::getMd5, md5Ids);
            }
        }
    })
    .eq(ChangeData::getUserId, userId)
    .list();
```

### 2. 结果组装逻辑
```java
// 根据 ChangeEnum 的 queryType 决定使用哪个字段作为行key
ChangeEnum changeEnum = ChangeEnum.getEnumByType(changeType);
if (changeEnum != null) {
    String rowKey;
    if ("1".equals(changeEnum.getQueryType())) {
        // queryType = "1" 表示使用 md5 查询
        rowKey = changeData.getMd5();
    } else if ("2".equals(changeEnum.getQueryType())) {
        // queryType = "2" 表示使用 indexId 查询
        rowKey = String.valueOf(changeData.getIndexId());
    } else {
        // 默认使用 md5
        rowKey = changeData.getMd5();
    }
    
    if (rowKey != null) {
        resultTable.put(rowKey, changeType, changeValue);
    }
}
```

## 📝 使用示例

### 示例1：只查询 md5Ids
```java
List<String> md5Ids = Arrays.asList("abc123", "def456", "ghi789");
Table<String, Integer, String> result = changeDataService.getBatchChangeDataTable(null, md5Ids, userId);

// 获取结果
String riskGradeValue = result.get("abc123", 1); // 风险级别 (queryType="1", 使用md5作为key)
```

### 示例2：只查询 indexIds
```java
List<String> indexIds = Arrays.asList("1001", "1002", "1003");
Table<String, Integer, String> result = changeDataService.getBatchChangeDataTable(indexIds, null, userId);

// 获取结果
String tagValue = result.get("1001", 2); // 标签 (queryType="2", 使用indexId作为key)
```

### 示例3：同时查询 indexIds 和 md5Ids
```java
List<String> indexIds = Arrays.asList("1001", "1002");
List<String> md5Ids = Arrays.asList("abc123", "def456");
Table<String, Integer, String> result = changeDataService.getBatchChangeDataTable(indexIds, md5Ids, userId);

// 获取结果
String riskGradeValue = result.get("abc123", 1); // 风险级别 (使用md5作为key)
String tagValue = result.get("1001", 2);         // 标签 (使用indexId作为key)
```

## 🎯 返回结果结构

### Guava Table 结构
```
Table<String, Integer, String>
  ↓
行key(rowKey) → 列key(changeType) → 值(changeValue)
```

### 具体示例
```
resultTable:
├── "abc123" (md5)
│   └── 1 (RISK_GRADE) → "高风险"
├── "def456" (md5)  
│   └── 1 (RISK_GRADE) → "中风险"
├── "1001" (indexId)
│   └── 2 (TAG) → "重要标签"
└── "1002" (indexId)
    └── 2 (TAG) → "普通标签"
```

## ⚠️ 注意事项

1. **参数验证**：indexIds 和 md5Ids 至少要有一个不为空
2. **权限控制**：只查询指定用户的数据
3. **类型转换**：indexId 在作为行key时会转换为 String 类型
4. **空值处理**：如果 rowKey 为 null，不会添加到结果中

## 🔄 兼容性说明

### 对现有代码的影响
- **方法签名变更**：需要更新调用方的代码
- **返回类型优化**：从 `Object` 改为 `String`，类型更明确
- **功能增强**：支持更灵活的查询方式

### 迁移建议
```java
// 旧代码
Table<String, Integer, Object> result = changeDataService.getBatchChangeDataTable(md5Ids, userId);

// 新代码
Table<String, Integer, String> result = changeDataService.getBatchChangeDataTable(null, md5Ids, userId);
```

## 🚀 扩展性

如果将来需要添加新的 ChangeEnum 类型：

1. 在 ChangeEnum 中添加新的枚举值
2. 设置对应的 queryType（"1" 或 "2"）
3. 方法会自动根据 queryType 选择正确的字段作为行key

这种设计使得方法具有很好的扩展性和维护性。
