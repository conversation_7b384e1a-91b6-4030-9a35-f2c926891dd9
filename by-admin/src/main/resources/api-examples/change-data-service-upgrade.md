# ChangeDataService.getBatchChangeDataTable 方法改造说明

## 🔄 改造内容

### 原方法签名
```java
public Table<String, Integer, Object> getBatchChangeDataTable(List<String> md5Ids, Long userId)
```

### 新方法签名
```java
public Table<String, Integer, Object> getBatchChangeDataTable(List<String> indexIds, List<String> md5Ids, Long userId)
```

## 🎯 改造目标

1. **支持双重查询条件**：同时支持 indexId 和 md5Id 批量查询
2. **智能字段选择**：根据 ChangeEnum 的 queryType 决定返回结果的行key
3. **按changeType聚合处理**：不同类型的数据采用不同的处理逻辑
4. **TAG特殊处理**：TAG类型需要根据changeValue（tag id）查询标签信息，并按indexId聚合
5. **兼容现有逻辑**：保持与后续获取逻辑的兼容性

## 📊 ChangeEnum 配置说明

根据 ChangeEnum 的定义：

| 枚举值 | 名称 | type | queryType | 说明 |
|--------|------|------|-----------|------|
| RISK_GRADE | 风险级别 | 1 | "1" | 使用 md5 查询 |
| TAG | 标签 | 2 | "2" | 使用 indexId 查询 |

### queryType 含义
- `"1"` - 表示使用 md5 查询，返回结果以 md5 作为行key
- `"2"` - 表示使用 indexId 查询，返回结果以 indexId 作为行key

## 🔧 核心逻辑

### 1. 查询逻辑
```java
// 支持 indexIds 和 md5Ids 的联合查询
List<ChangeData> changeDataList = this.lambdaQuery()
    .and(wrapper -> {
        // 根据indexIds查询（如果有）
        if (CollUtil.isNotEmpty(indexIds)) {
            wrapper.in(ChangeData::getIndexId, indexIds);
        }
        // 根据md5Ids查询（如果有）
        if (CollUtil.isNotEmpty(md5Ids)) {
            if (CollUtil.isNotEmpty(indexIds)) {
                wrapper.or().in(ChangeData::getMd5, md5Ids);
            } else {
                wrapper.in(ChangeData::getMd5, md5Ids);
            }
        }
    })
    .eq(ChangeData::getUserId, userId)
    .list();
```

### 2. 按changeType分组处理
```java
// 按 changeType 分组处理
Map<Integer, List<ChangeData>> changeTypeGroups = changeDataList.stream()
        .collect(Collectors.groupingBy(ChangeData::getChangeType));

// 处理每种 changeType
changeTypeGroups.forEach((changeType, dataList) -> {
    ChangeEnum changeEnum = ChangeEnum.getEnumByType(changeType);
    if (changeEnum == null) {
        return;
    }

    if (ChangeEnum.TAG.equals(changeEnum)) {
        // TAG 类型特殊处理
        processTagChangeData(dataList, resultTable, userId);
    } else {
        // 其他类型（如 RISK_GRADE）直接处理
        processNormalChangeData(dataList, resultTable, changeEnum);
    }
});
```

### 3. TAG特殊处理逻辑
```java
private void processTagChangeData(List<ChangeData> dataList, Table<String, Integer, Object> resultTable, Long userId) {
    // 按 indexId 分组
    Map<Long, List<ChangeData>> indexGroups = dataList.stream()
            .collect(Collectors.groupingBy(ChangeData::getIndexId));

    // 收集所有的 tag id
    Set<Long> tagIds = dataList.stream()
            .map(ChangeData::getChangeValue)
            .filter(StrUtil::isNotBlank)
            .map(Long::valueOf)
            .collect(Collectors.toSet());

    // 批量查询标签信息
    Map<Long, Tag> tagMap = tagService.lambdaQuery()
            .in(Tag::getId, tagIds)
            .eq(Tag::getUserId, userId)
            .eq(Tag::getDelFlag, "0")
            .list()
            .stream()
            .collect(Collectors.toMap(Tag::getId, Function.identity()));

    // 为每个 indexId 组装标签列表
    indexGroups.forEach((indexId, indexDataList) -> {
        List<TagSimpleVO> tagList = indexDataList.stream()
                .map(ChangeData::getChangeValue)
                .filter(StrUtil::isNotBlank)
                .map(Long::valueOf)
                .map(tagMap::get)
                .filter(Objects::nonNull)
                .map(tag -> {
                    TagSimpleVO vo = new TagSimpleVO();
                    vo.setId(tag.getId());
                    vo.setTagContent(tag.getTagContent());
                    return vo;
                })
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(tagList)) {
            resultTable.put(String.valueOf(indexId), ChangeEnum.TAG.getType(), tagList);
        }
    });
}
```

### 4. 普通类型处理逻辑
```java
private void processNormalChangeData(List<ChangeData> dataList, Table<String, Integer, Object> resultTable, ChangeEnum changeEnum) {
    for (ChangeData changeData : dataList) {
        String changeValue = changeData.getChangeValue();

        String rowKey;
        if ("1".equals(changeEnum.getQueryType())) {
            // queryType = "1" 表示使用 md5 查询
            rowKey = changeData.getMd5();
        } else if ("2".equals(changeEnum.getQueryType())) {
            // queryType = "2" 表示使用 indexId 查询
            rowKey = String.valueOf(changeData.getIndexId());
        } else {
            // 默认使用 md5
            rowKey = changeData.getMd5();
        }

        if (rowKey != null && StrUtil.isNotBlank(changeValue)) {
            resultTable.put(rowKey, changeEnum.getType(), changeValue);
        }
    }
}
```

## 📝 使用示例

### 示例1：只查询 md5Ids（风险级别）
```java
List<String> md5Ids = Arrays.asList("abc123", "def456", "ghi789");
Table<String, Integer, Object> result = changeDataService.getBatchChangeDataTable(null, md5Ids, userId);

// 获取风险级别结果
String riskGradeValue = (String) result.get("abc123", 1); // 风险级别 (queryType="1", 使用md5作为key)
```

### 示例2：只查询 indexIds（标签）
```java
List<String> indexIds = Arrays.asList("1001", "1002", "1003");
Table<String, Integer, Object> result = changeDataService.getBatchChangeDataTable(indexIds, null, userId);

// 获取标签结果
List<TagSimpleVO> tagList = (List<TagSimpleVO>) result.get("1001", 2); // 标签列表 (queryType="2", 使用indexId作为key)
```

### 示例3：同时查询 indexIds 和 md5Ids
```java
List<String> indexIds = Arrays.asList("1001", "1002");
List<String> md5Ids = Arrays.asList("abc123", "def456");
Table<String, Integer, Object> result = changeDataService.getBatchChangeDataTable(indexIds, md5Ids, userId);

// 获取风险级别结果（使用md5作为key）
String riskGradeValue = (String) result.get("abc123", 1);

// 获取标签结果（使用indexId作为key）
List<TagSimpleVO> tagList = (List<TagSimpleVO>) result.get("1001", 2);
```

## 🎯 返回结果结构

### Guava Table 结构
```
Table<String, Integer, Object>
  ↓
行key(rowKey) → 列key(changeType) → 值(处理后的值)
```

### 具体示例
```
resultTable:
├── "abc123" (md5)
│   └── 1 (RISK_GRADE) → "高风险" (String)
├── "def456" (md5)
│   └── 1 (RISK_GRADE) → "中风险" (String)
├── "1001" (indexId)
│   └── 2 (TAG) → [TagSimpleVO{id=123, tagContent="重要标签"}, TagSimpleVO{id=124, tagContent="工作标签"}] (List<TagSimpleVO>)
└── "1002" (indexId)
    └── 2 (TAG) → [TagSimpleVO{id=125, tagContent="普通标签"}] (List<TagSimpleVO>)
```

### 数据类型说明
- **RISK_GRADE**: 返回 `String` 类型的风险级别值
- **TAG**: 返回 `List<TagSimpleVO>` 类型的标签列表，每个标签包含 id 和 tagContent

## ⚠️ 注意事项

1. **参数验证**：indexIds 和 md5Ids 至少要有一个不为空
2. **权限控制**：只查询指定用户的数据
3. **类型转换**：indexId 在作为行key时会转换为 String 类型
4. **空值处理**：如果 rowKey 为 null，不会添加到结果中

## 🔄 兼容性说明

### 对现有代码的影响
- **方法签名变更**：需要更新调用方的代码，增加 indexIds 参数
- **返回类型保持**：仍然是 `Table<String, Integer, Object>`，但值的类型更丰富
- **功能增强**：支持更灵活的查询方式和智能的数据处理

### 迁移建议
```java
// 旧代码
Table<String, Integer, Object> result = changeDataService.getBatchChangeDataTable(md5Ids, userId);

// 新代码 - 只查询风险级别
Table<String, Integer, Object> result = changeDataService.getBatchChangeDataTable(null, md5Ids, userId);

// 新代码 - 同时查询风险级别和标签
Table<String, Integer, Object> result = changeDataService.getBatchChangeDataTable(indexIds, md5Ids, userId);
```

### 调用方代码更新
```java
// SearchServiceImpl 中的更新
// 旧代码
changeDataTable = changeDataService.getBatchChangeDataTable(md5List, SecurityUtils.getUserIdL());

// 新代码
changeDataTable = changeDataService.getBatchChangeDataTable(indexList, md5List, SecurityUtils.getUserIdL());

// InfoService 中的更新
// 旧代码
Table<String, Integer, Object> changeDataTable = changeDataService.getBatchChangeDataTable(CollUtil.newArrayList(md5), SecurityUtils.getUserIdL());
Object tag = changeDataTable.get(md5, ChangeEnum.TAG.getType());

// 新代码
Table<String, Integer, Object> changeDataTable = changeDataService.getBatchChangeDataTable(
    CollUtil.newArrayList(indexId), CollUtil.newArrayList(md5), SecurityUtils.getUserIdL());
Object tag = changeDataTable.get(indexId, ChangeEnum.TAG.getType()); // 注意：TAG使用indexId作为key
```

## 🚀 扩展性

如果将来需要添加新的 ChangeEnum 类型：

1. 在 ChangeEnum 中添加新的枚举值
2. 设置对应的 queryType（"1" 或 "2"）
3. 方法会自动根据 queryType 选择正确的字段作为行key

这种设计使得方法具有很好的扩展性和维护性。
